import { useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import Contact from "@/components/Contact";
import {
  PenTool,
  Globe,
  TrendingUp,
  Target,
  Clock,
  CheckCircle,
  Star,
  ArrowRight,
  FileText,
  Search,
  Users,
  Zap,
  Award,
  BarChart3,
  MessageCircle,
  DollarSign,
} from "lucide-react";

const ContentWriting = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [servicesRef, servicesInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [processRef, processInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  useEffect(() => {
    document.title = "Content Writing Services - The Brown Patience Company";
  }, []);

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const servicesSpring = useSpring({
    opacity: servicesInView ? 1 : 0,
    transform: servicesInView ? "translateY(0px)" : "translateY(50px)",
  });

  const processSpring = useSpring({
    opacity: processInView ? 1 : 0,
    transform: processInView ? "translateY(0px)" : "translateY(50px)",
  });

  // Content writing process steps
  const contentProcess = [
    {
      step: "1",
      title: "Tell Me What You Need",
      description:
        "You send me a message telling me what you want me to write for you. What's the subject? Is it a newsletter? Is it a blog? A landing page? Product description?",
      icon: MessageCircle,
    },
    {
      step: "2",
      title: "Questions & Samples",
      description:
        "A few questions run in-between. If you have samples of the kind of content you want me to create, great. If not, no worries.",
      icon: Search,
    },
    {
      step: "3",
      title: "Settle on Price",
      description: "We settle on a price. This is mostly based on word count.",
      icon: DollarSign,
    },
    {
      step: "4",
      title: "Content Delivery",
      description: "I send you the content.",
      icon: FileText,
    },
    {
      step: "5",
      title: "Review & Revisions",
      description:
        "You approve the content or point out the changes you need. They get done.",
      icon: CheckCircle,
    },
    {
      step: "6",
      title: "Repeat Process",
      description: "We do it again and again.",
      icon: Zap,
    },
  ];

  const contentBenefits = [
    "Increased website traffic and engagement",
    "Higher search engine rankings",
    "Improved conversion rates",
    "Enhanced brand authority and credibility",
    "Consistent brand voice across all platforms",
    "Time savings for your team",
  ];

  const scrollToContact = () => {
    document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <div className="min-h-screen bg-cream-50 pt-16">
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="py-20 bg-gradient-to-br from-brand-primary to-brand-neutral/20"
      >
        <div className="container mx-auto px-4">
          <animated.div
            style={heroSpring}
            className="max-w-4xl mx-auto text-center"
          >
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-6">
              <PenTool className="w-4 h-4" />
              Professional Content Writing Services
            </div>
            <h1 className="text-4xl md:text-6xl font-serif font-bold text-brand-secondary mb-6">
              Professional Content Writing Services
            </h1>
            <p className="text-xl text-brand-secondary/70 mb-8 leading-relaxed">
              You tell me you need content and I ask, "Which kind?" Social media
              content? Web copies? Newsletters? Blog posts? If you can just tell
              me what you need, you'll get it.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="hero"
                size="lg"
                onClick={scrollToContact}
                className="group"
              >
                Get Started Today
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                View Portfolio
              </Button>
            </div>
          </animated.div>
        </div>
      </section>

      {/* Overview Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-8">
                Content Writing Services
              </h2>
              <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant">
                <p className="text-lg text-brand-secondary/80 leading-relaxed mb-6">
                  Content shouldn't trouble you. Outsource the writing of the
                  newsletter, the blog, the website content, including the
                  existing web content you'd like to edit. Carry on with your
                  key tasks; the content writer will handle the words.
                </p>
                <p className="text-lg text-brand-secondary/80 leading-relaxed">
                  Whether in South Africa, down in London, or anywhere in the
                  world, people want to read interesting pieces. So I take the
                  important message you want to get out there and transform it
                  into an interesting piece—the one that keeps the reader
                  reading, the one that clearly communicates the message, the
                  one that gets your reader to where you intended.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section ref={processRef} className="py-20 bg-brand-neutral/30">
        <div className="container mx-auto px-4">
          <animated.div style={processSpring}>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                How Do I Become Your Content Writer?
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                A strategic approach that ensures every piece of content serves
                your business goals.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {contentProcess.map((step, index) => {
                const IconComponent = step.icon;
                return (
                  <animated.div
                    key={step.step}
                    style={{
                      opacity: processInView ? 1 : 0,
                      transform: processInView
                        ? "translateY(0px)"
                        : "translateY(50px)",
                      transitionDelay: processInView
                        ? `${200 + index * 100}ms`
                        : "0ms",
                      transition: "all 0.6s ease-out",
                    }}
                  >
                    <Card className="h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                      <CardHeader>
                        <div className="flex items-center gap-4 mb-4">
                          <div className="w-12 h-12 bg-brand-accent rounded-full flex items-center justify-center relative">
                            <IconComponent className="w-6 h-6 text-brand-primary" />
                            <div className="absolute -top-2 -right-2 w-6 h-6 bg-brand-secondary text-brand-primary rounded-full flex items-center justify-center text-xs font-bold">
                              {step.step}
                            </div>
                          </div>
                          <h3 className="text-lg font-serif font-bold text-brand-secondary">
                            {step.title}
                          </h3>
                        </div>
                        <p className="text-brand-secondary/70 leading-relaxed">
                          {step.description}
                        </p>
                      </CardHeader>
                    </Card>
                  </animated.div>
                );
              })}
            </div>
          </animated.div>
        </div>
      </section>

      {/* Contact Section */}
      <Contact
        title="Ready to Get Your Content Written?"
        subtitle="Let's Work Together"
        description="Tell me what you need and I'll transform your important message into interesting, engaging content that keeps readers reading and gets them to where you intended."
        whatsappMessage="Hi! I need help with content writing. Could we discuss my project and how you can help me create engaging content for my business?"
        backgroundColor="bg-brand-neutral/30"
      />
    </div>
  );
};

export default ContentWriting;
